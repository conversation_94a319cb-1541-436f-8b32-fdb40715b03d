package com.chidhagni.donationreceipt.userauth.dto.response;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.UUID;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class UserRegistrationResponse {

    private UUID userId;
    private String email;
    private String name;
    private String mobileNumber;
    private boolean emailVerified;
    private boolean isActive;
    private LocalDateTime createdOn;
    private String message;

    // Static factory methods for common responses
    public static UserRegistrationResponse success(UUID userId, String email, String name, String mobileNumber, LocalDateTime createdOn) {
        return UserRegistrationResponse.builder()
                .userId(userId)
                .email(email)
                .name(name)
                .mobileNumber(mobileNumber)
                .emailVerified(false)
                .isActive(true)
                .createdOn(createdOn)
                .message("Registration successful. Please check your email for verification link.")
                .build();
    }

    public static UserRegistrationResponse emailAlreadyExists() {
        return UserRegistrationResponse.builder()
                .message("Email address is already registered. Please use a different email or try logging in.")
                .build();
    }
}
